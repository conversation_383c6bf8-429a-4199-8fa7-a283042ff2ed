require('dotenv').config();

const config = {
  // API Configuration
  geminiApiKey: process.env.GEMINI_API_KEY,
  geminiApiBaseUrl: process.env.GEMINI_API_BASE_URL || 'https://generativelanguage.googleapis.com/v1beta/models',
  imagenApiBaseUrl: process.env.IMAGEN_API_BASE_URL || 'https://generativelanguage.googleapis.com/v1beta/models',
  
  // Rate Limiting
  apiRateLimitDelay: parseInt(process.env.API_RATE_LIMIT_DELAY) || 1000,
  maxRetries: parseInt(process.env.MAX_RETRIES) || 3,
  
  // File Configuration
  outputDir: process.env.OUTPUT_DIR || './output',
  enableDebugLogging: process.env.ENABLE_DEBUG_LOGGING === 'true',
  maxFileSizeMB: parseInt(process.env.MAX_FILE_SIZE_MB) || 10,
  supportedFormats: (process.env.SUPPORTED_FORMATS || 'md,html,txt').split(','),
  
  // Model Names
  models: {
    gemini: 'gemini-2.5-flash',
    imagen: 'imagen-3.0-generate-002'
  },
  
  // Hardcoded Prompts for each step
  prompts: {
    // Step 1: Document Analysis
    documentAnalysis: `Analyze this document and identify all locations where images would enhance the content. 
    For each location, provide:
    1. A detailed description of what image should be placed there
    2. The context around that location
    3. How the image would support the content
    
    Format your response as markdown with clear sections for each identified image location.
    Use the format: [IMAGE_PLACEHOLDER_X] where X is a sequential number.
    
    After each [IMAGE_PLACEHOLDER_X], provide a detailed description of the image that should be generated.`,
    
    // Step 3: Description Analysis (for refining image descriptions)
    descriptionAnalysis: `Take this image description and enhance it for optimal text-to-image generation.
    
    Original description: {description}
    
    Please:
    1. Make the description more specific and detailed
    2. Add visual style elements (lighting, composition, color palette)
    3. Specify the artistic style or photographic approach
    4. Include technical details that would improve image quality
    5. Ensure the description is optimized for AI image generation
    
    Return only the enhanced description, nothing else.`,
    
    // Step 5: Final Analysis
    finalAnalysis: `Analyze this processed document with generated images and create a final summary image that represents the overall theme and content.
    
    Document content: {content}
    
    Create a description for a single, comprehensive image that:
    1. Captures the main theme of the document
    2. Represents the key concepts visually
    3. Could serve as a cover image or hero image for this content
    4. Is visually appealing and professional
    
    Return only the image description for generation, nothing else.`
  }
};

// Validation
if (!config.geminiApiKey) {
  console.error('Error: GEMINI_API_KEY is required. Please set it in your .env file.');
  process.exit(1);
}

module.exports = config;
