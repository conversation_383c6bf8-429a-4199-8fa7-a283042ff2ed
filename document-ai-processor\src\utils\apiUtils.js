const axios = require('axios');
const config = require('../config');
const FileUtils = require('./fileUtils');

class ApiUtils {
  /**
   * Sleep function for rate limiting
   */
  static async sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Make API request with retry logic
   */
  static async makeApiRequest(url, data, headers = {}, retries = config.maxRetries) {
    try {
      await FileUtils.logToFile(`Making API request to: ${url}`);
      
      const response = await axios.post(url, data, {
        headers: {
          'Content-Type': 'application/json',
          'x-goog-api-key': config.geminiApiKey,
          ...headers
        },
        timeout: 60000 // 60 second timeout
      });

      await FileUtils.logToFile(`API request successful: ${response.status}`);
      return response.data;
    } catch (error) {
      const errorDetails = {
        message: error.message,
        status: error.response?.status,
        statusText: error.response?.statusText,
        data: error.response?.data,
        code: error.code
      };

      await FileUtils.logToFile(`API request failed: ${JSON.stringify(errorDetails, null, 2)}`);

      if (retries > 0 && (error.response?.status >= 500 || error.code === 'ECONNRESET')) {
        console.log(`API request failed, retrying... (${retries} attempts left)`);
        await this.sleep(config.apiRateLimitDelay * 2);
        return this.makeApiRequest(url, data, headers, retries - 1);
      }

      // 提供更详细的错误信息
      let errorMessage = 'API request failed';
      if (error.response?.status) {
        errorMessage += ` (HTTP ${error.response.status})`;
      }
      if (error.response?.data?.error?.message) {
        errorMessage += `: ${error.response.data.error.message}`;
      } else if (error.message) {
        errorMessage += `: ${error.message}`;
      }

      throw new Error(errorMessage);
    }
  }

  /**
   * Call Gemini API for text generation
   */
  static async callGeminiApi(prompt, model = config.models.gemini) {
    const url = `${config.geminiApiBaseUrl}/${model}:generateContent`;
    
    const requestData = {
      contents: [
        {
          parts: [
            {
              text: prompt
            }
          ]
        }
      ],
      generationConfig: {
        temperature: 0.7,
        topK: 40,
        topP: 0.95,
        maxOutputTokens: 8192
      }
    };

    const response = await this.makeApiRequest(url, requestData);
    
    if (!response.candidates || !response.candidates[0] || !response.candidates[0].content) {
      throw new Error('Invalid response from Gemini API');
    }

    return response.candidates[0].content.parts[0].text;
  }

  /**
   * Call Imagen API for image generation
   */
  static async callImagenApi(prompt, model = config.models.imagen) {
    const url = `${config.imagenApiBaseUrl}/${model}:generateImages`;
    
    const requestData = {
      prompt: prompt,
      sampleCount: 1,
      aspectRatio: "1:1",
      safetyFilterLevel: "BLOCK_ONLY_HIGH",
      personGeneration: "ALLOW_ADULT"
    };

    const response = await this.makeApiRequest(url, requestData);
    
    if (!response.generatedImages || response.generatedImages.length === 0) {
      throw new Error('No images generated from Imagen API');
    }

    // Return the base64 image data or URL
    return response.generatedImages[0];
  }

  /**
   * Rate limit API calls
   */
  static async rateLimitedCall(apiFunction, ...args) {
    const result = await apiFunction(...args);
    await this.sleep(config.apiRateLimitDelay);
    return result;
  }

  /**
   * Batch API calls with rate limiting
   */
  static async batchApiCalls(apiFunction, argsArray, batchSize = 3) {
    const results = [];
    
    for (let i = 0; i < argsArray.length; i += batchSize) {
      const batch = argsArray.slice(i, i + batchSize);
      const batchPromises = batch.map(args => this.rateLimitedCall(apiFunction, ...args));
      
      const batchResults = await Promise.all(batchPromises);
      results.push(...batchResults);
      
      // Additional delay between batches
      if (i + batchSize < argsArray.length) {
        await this.sleep(config.apiRateLimitDelay * 2);
      }
    }
    
    return results;
  }
}

module.exports = ApiUtils;
