const axios = require('axios');
require('dotenv').config();

async function testGeminiAPI() {
  console.log('🧪 测试 Gemini API 连接...');
  
  const apiKey = process.env.GEMINI_API_KEY;
  console.log('API Key:', apiKey ? `${apiKey.substring(0, 10)}...` : '未配置');
  
  // 测试不同的模型名称
  const modelsToTest = [
    'gemini-2.5-flash',
    'gemini-1.5-flash',
    'gemini-1.5-pro',
    'gemini-pro'
  ];
  
  for (const model of modelsToTest) {
    console.log(`\n📡 测试模型: ${model}`);
    
    const url = `https://generativelanguage.googleapis.com/v1beta/models/${model}:generateContent`;
    console.log('请求URL:', url);
    
    const requestData = {
      contents: [
        {
          parts: [
            {
              text: "Hello, this is a test message. Please respond with 'API connection successful'."
            }
          ]
        }
      ],
      generationConfig: {
        temperature: 0.1,
        maxOutputTokens: 100
      }
    };
    
    try {
      const response = await axios.post(url, requestData, {
        headers: {
          'Content-Type': 'application/json',
          'x-goog-api-key': apiKey
        },
        timeout: 30000 // 30秒超时
      });
      
      console.log('✅ 成功!');
      console.log('响应状态:', response.status);
      if (response.data.candidates && response.data.candidates[0]) {
        console.log('响应内容:', response.data.candidates[0].content.parts[0].text);
      }
      
      // 找到一个工作的模型就停止
      console.log(`\n🎉 找到可用的模型: ${model}`);
      return model;
      
    } catch (error) {
      console.log('❌ 失败');
      console.log('错误类型:', error.code || 'Unknown');
      console.log('HTTP状态:', error.response?.status || 'N/A');
      console.log('错误信息:', error.response?.data?.error?.message || error.message);
      
      if (error.response?.data) {
        console.log('详细错误:', JSON.stringify(error.response.data, null, 2));
      }
    }
  }
  
  console.log('\n❌ 所有模型测试都失败了');
  return null;
}

// 运行测试
testGeminiAPI().then(workingModel => {
  if (workingModel) {
    console.log(`\n💡 建议在 src/config.js 中使用模型: ${workingModel}`);
  } else {
    console.log('\n💡 可能的解决方案:');
    console.log('1. 检查网络连接');
    console.log('2. 验证API密钥是否有效');
    console.log('3. 检查是否需要代理设置');
    console.log('4. 确认Google AI Studio账户状态');
  }
}).catch(console.error);
